import { useEffect } from 'react';
import { XMarkIcon, UserIcon, EnvelopeIcon, PhoneIcon, MapPinIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { Order } from '@/types/order';
import OrderStatusBadge from './OrderStatusBadge';

interface OrderDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: Order | null;
}

export default function OrderDetailModal({ isOpen, onClose, order }: OrderDetailModalProps) {


  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !order) return null;

  // Helper function to get customer information from various sources
  const getCustomerInfo = () => {
    const metadataCustomer = order.metadata?.customer;
    return {
      name: order.customer?.name ||
            order.metadata?.customerName ||
            order.shippingAddress?.name ||
            (metadataCustomer ? `${metadataCustomer.firstName || ''} ${metadataCustomer.lastName || ''}`.trim() : '') ||
            'N/A',
      email: order.customer?.email ||
             order.metadata?.customerEmail ||
             metadataCustomer?.email ||
             'N/A',
      phone: order.customer?.phone ||
             order.metadata?.customerPhone ||
             order.shippingAddress?.phone ||
             metadataCustomer?.phone ||
             'N/A',
      address: order.customer?.address ||
               order.metadata?.shippingAddress ||
               (order.shippingAddress ?
                 `${order.shippingAddress.address || ''}, ${order.shippingAddress.city || ''}, ${order.shippingAddress.postalCode || ''}, ${order.shippingAddress.country || ''}`.replace(/^,\s*|,\s*$/g, '').replace(/,\s*,/g, ',')
                 : 'N/A')
    };
  };

  // Helper function to get products from order (handles both products and items fields)
  const getOrderProducts = () => {
    if (order.items && order.items.length > 0) {
      // Convert items to products format
      return order.items.map(item => ({
        _id: item._id || item.productId,
        name: item.name,
        price: item.price,
        image: item.image,
        quantity: item.quantity,
        description: item.description,
        category: item.category
      }));
    }
    return order.products || [];
  };

  const customerInfo = getCustomerInfo();
  const products = getOrderProducts();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-gray-200 bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-2 sm:p-4">
        <div className="relative w-full max-w-6xl transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all max-h-[95vh] overflow-y-auto">
          {/* Header */}
          <div className="sticky top-0 bg-white border-b border-gray-200 px-4 sm:px-6 py-4 z-10">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900">
                  Order Details
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  Order #{order._id?.slice(-8) || 'N/A'}
                </p>
              </div>
              <button
                type="button"
                className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 p-2"
                onClick={onClose}
              >
                <span className="sr-only">Close</span>
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="px-4 sm:px-6 py-6">
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              {/* Order Information */}
              <div className="xl:col-span-1 space-y-6">
                {/* Order Summary */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 rounded-xl border border-blue-100">
                  <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                    <DocumentTextIcon className="w-5 h-5 mr-2 text-blue-600" />
                    Order Summary
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 text-sm">Order ID:</span>
                      <span className="font-medium text-gray-900">#{order._id?.slice(-8) || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 text-sm">Status:</span>
                      <OrderStatusBadge status={order.status} />
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 text-sm">Total Amount:</span>
                      <span className="font-bold text-lg text-green-600">{formatCurrency(order.total)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 text-sm">Order Date:</span>
                      <span className="font-medium text-gray-900">{formatDate(order.createdAt)}</span>
                    </div>
                    {order.updatedAt !== order.createdAt && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600 text-sm">Last Updated:</span>
                        <span className="font-medium text-gray-900">{formatDate(order.updatedAt)}</span>
                      </div>
                    )}
                    {(order.paymentMethod || order.metadata?.paymentMethod) && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600 text-sm">Payment Method:</span>
                        <span className="font-medium text-gray-900 capitalize">
                          {(order.paymentMethod || order.metadata?.paymentMethod || '').replace(/_/g, ' ')}
                        </span>
                      </div>
                    )}
                    {order.paymentStatus && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600 text-sm">Payment Status:</span>
                        <span className={`font-medium capitalize ${
                          order.paymentStatus === 'paid' ? 'text-green-600' :
                          order.paymentStatus === 'failed' ? 'text-red-600' : 'text-yellow-600'
                        }`}>
                          {order.paymentStatus}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Customer Information */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 sm:p-6 rounded-xl border border-green-100">
                  <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                    <UserIcon className="w-5 h-5 mr-2 text-green-600" />
                    Customer Information
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <UserIcon className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-600">Name</p>
                        <p className="font-medium text-gray-900 break-words">{customerInfo.name}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <EnvelopeIcon className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-600">Email</p>
                        <p className="font-medium text-gray-900 break-all">{customerInfo.email}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <PhoneIcon className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-600">Phone</p>
                        <p className="font-medium text-gray-900">{customerInfo.phone}</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <MapPinIcon className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-600">Shipping Address</p>
                        <p className="font-medium text-gray-900 break-words">{customerInfo.address}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Products */}
              <div className="xl:col-span-2">
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 sm:p-6 rounded-xl border border-purple-100">
                  <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                    Ordered Products ({products.length} item{products.length !== 1 ? 's' : ''})
                  </h4>

                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                    {products.map((product, index) => (
                      <div key={product._id || index} className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                        <div className="flex items-start space-x-4">
                          {/* Product Image */}
                          <div className="flex-shrink-0">
                            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-100 rounded-lg overflow-hidden">
                              {product.image ? (
                                <img
                                  src={product.image}
                                  alt={product.name}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = '/placeholder-product.svg';
                                  }}
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center bg-gray-200">
                                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                  </svg>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Product Details */}
                          <div className="flex-1 min-w-0">
                            <h5 className="text-sm font-semibold text-gray-900 mb-1 line-clamp-2">
                              {product.name}
                            </h5>
                            <div className="space-y-1">
                              <p className="text-lg font-bold text-green-600">
                                {formatCurrency(product.price)}
                              </p>
                              {product.quantity && (
                                <p className="text-sm text-gray-500">
                                  Quantity: {product.quantity}
                                </p>
                              )}
                              {product.category && (
                                <p className="text-xs text-gray-400">
                                  Category: {product.category}
                                </p>
                              )}
                              {product.description && (
                                <p className="text-xs text-gray-500 line-clamp-2 mt-2">
                                  {product.description}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Notes Section */}
          {(order.notes || order.metadata?.notes) && (
            <div className="px-4 sm:px-6 pb-6">
              <div className="bg-gradient-to-r from-amber-50 to-orange-50 p-4 sm:p-6 rounded-xl border border-amber-100">
                <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                  <DocumentTextIcon className="w-5 h-5 mr-2 text-amber-600" />
                  Order Notes
                </h4>
                <p className="text-sm text-gray-700 leading-relaxed">
                  {order.notes || order.metadata?.notes}
                </p>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="sticky bottom-0 bg-white border-t border-gray-200 px-4 sm:px-6 py-4">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <div className="text-sm text-gray-500">
                Order created on {formatDate(order.createdAt)}
              </div>
              <div className="flex space-x-3">
                <button
                  type="button"
                  className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                  onClick={onClose}
                >
                  Close
                </button>
                <button
                  type="button"
                  className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                  onClick={() => window.print()}
                >
                  Print Order
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
