import axios from 'axios';

// API base URL - using the proxied URL to avoid CORS issues
const API_BASE_URL = '/api';
console.log('API Base URL (proxied):', API_BASE_URL);

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Set withCredentials to true since the backend is configured with credentials
  withCredentials: true,
  // Add a timeout to prevent hanging requests
  timeout: 15000, // Increased timeout to 15 seconds
});

// Request interceptor to add auth token if available
apiClient.interceptors.request.use(
  (config) => {
    // Get token from localStorage if available
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    // Log successful responses for debugging
    console.log(`API Success [${response.config.method?.toUpperCase()}] ${response.config.url}:`, response.status);
    return response;
  },
  (error) => {
    // Handle common errors here
    if (error.response) {
      // Server responded with an error status
      console.error('API Error:', error.response.status, error.response.data);
      console.error('Request URL:', error.config.url);
      console.error('Request Method:', error.config.method);
      console.error('Request Data:', error.config.data);

      // Handle 401 Unauthorized - could redirect to login
      if (error.response.status === 401) {
        // Don't automatically log out for password change errors
        if (error.config.url?.includes('/password')) {
          console.log('Password change authentication error - not logging out user');
        } else {
          // Clear auth data and redirect to login if needed
          if (typeof window !== 'undefined') {
            localStorage.removeItem('token');
            localStorage.removeItem('userId');
            localStorage.removeItem('name');
            localStorage.removeItem('email');
            localStorage.removeItem('avatar');

            // Only redirect if we're not already on the login page
            const currentPath = window.location.pathname;
            if (currentPath !== '/sign-in' && currentPath !== '/sign-up') {
              console.log('Redirecting to login due to 401 error');
              window.location.href = '/auth/login';
            }
          }
        }
      }

      // Handle 403 Forbidden
      if (error.response.status === 403) {
        console.error('Forbidden access:', error.response.data);
      }

      // Handle 404 Not Found
      if (error.response.status === 404) {
        console.error('Resource not found:', error.response.data);
      }

      // Handle 500 Server Error
      if (error.response.status >= 500) {
        console.error('Server error:', error.response.data);
      }
    } else if (error.request) {
      // Request was made but no response received
      console.error('Network Error - No response received:', error.request);
      console.error('Request URL:', error.config.url);
      console.error('Request Method:', error.config.method);
      console.error('Request Data:', error.config.data);

      // Check if it's a timeout
      if (error.code === 'ECONNABORTED') {
        console.error('Request timeout. Please check your internet connection.');
      }
    } else {
      // Something else happened
      console.error('Error:', error.message);
      if (error.config) {
        console.error('Request URL:', error.config.url);
        console.error('Request Method:', error.config.method);
        console.error('Request Data:', error.config.data);
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
