'use client';
import React ,{
    createContext,
    useState,
    useEffect,
    ReactNode,
    useContext,
} from 'react'
import { useRouter } from 'next/navigation';
import {toast} from 'sonner'
import {AuthState, AuthContextType} from '../types/user';
import apiClient from '@/lib/api/apiClient';

const initialState:AuthState={
    user:null,
    isAuthenticated:false,
    isLoading:true,
    error:null
}
const AuthContext=createContext<AuthContextType|undefined>(undefined);
export const AuthProvider=({ children }: { children: ReactNode }) =>{
    const [authState,setAuthState]=useState<AuthState>(initialState)
    const router=useRouter()

    useEffect(()=>{
        const checkAuth = ()=>{
            try {
                const token=localStorage.getItem('token')
            const userId=localStorage.getItem('userId');
            const name=localStorage.getItem('name')
            const email=localStorage.getItem('email')
            const phone=localStorage.getItem('phone')
            const isAdmin=localStorage.getItem('isAdmin')
            const createdAt=localStorage.getItem('createdAt')
            if(token && userId){
                setAuthState(
                    {
                        user:{
                            id:userId,
                            name:name ||'User',
                            email:email ||'',
                            phone:phone ||'',
                            isAdmin:isAdmin === 'true',
                            createdAt:createdAt || new Date().toISOString()
                        },
                        isAuthenticated:true,
                        isLoading:false,
                        error:null

                    }
                )
            }
            else{
                setAuthState({
                    ...initialState,
                    isLoading:false
                })
            }
        }
     catch (error) {
                console.log(error)
                setAuthState({
                    ...initialState,
                    isLoading:false
                })
            }
        }
        checkAuth()
        },[]);
        const login=async  (email:string,password:string)=>{
        setAuthState((prev)=>({
            ...prev,isLoading:true,error:null
        }))
        try {
            const response=await apiClient.post('/auth/login',{
                email,password
            })
            console.log("Login response:", response.data);
        if (response.data.status === 'success') {
        const { user, token } = response.data.data;

        // Store auth data in localStorage
        localStorage.setItem('token', token);
        localStorage.setItem('userId', user.id);
        localStorage.setItem('name', user.name);
        localStorage.setItem('email', user.email);
        localStorage.setItem('phone', user.phone || '');
        localStorage.setItem('isAdmin', user.isAdmin ? 'true' : 'false');
        localStorage.setItem('createdAt', user.createdAt || new Date().toISOString());

        // Update auth state
        setAuthState({
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            phone: user.phone || '',
            isAdmin: user.isAdmin,
            createdAt: user.createdAt || new Date().toISOString(),
          },
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        toast.success('Logged in successfully!');

        // Check if user is admin
        // if (user.isAdmin) {
          router.push('/dashboard');
        // } else {
        //   throw new Error('Access denied. Admin privileges required.');
        // }
      } else {
        throw new Error('Login failed');
      }
    } catch (error: unknown) {
      console.error('Login error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Invalid email or password';
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      toast.error(errorMessage);
    }
  };
  // Logout function
  const logout = () => {
    // Clear localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
    localStorage.removeItem('name');
    localStorage.removeItem('email');
    localStorage.removeItem('phone');
    localStorage.removeItem('isAdmin');
    localStorage.removeItem('createdAt');

    // Reset auth state
    setAuthState(initialState);

    // Redirect to login
    router.push('/auth/login');

    toast.success('Logged out successfully!');
  };

  // Function to directly set auth state (used by auth-callback)
  const setAuthStateDirectly = (state: AuthState) => {
    setAuthState(state);
  };

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        login,
        logout,
        setAuthState: setAuthStateDirectly,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};




